const request = require('../../utils/request');
const api = require('../../utils/API');
const app = getApp();

Page({
  data: {
    messages: [],
    inputValue: '',
    question: '',
    scrollToView: '',
    isFocused: false,
    isLoading: false,
    lastMessageTime: new Date().getTime()
  },

  onLoad() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: 'AI 智能助手'
    });

    // 隐藏导航栏的返回按钮（如果需要）
    // wx.hideHomeButton();
  },

  // 处理输入变化
  handleInput(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  // 处理输入框获取焦点
  handleFocus() {
    this.setData({
      isFocused: true
    });
  },

  // 处理输入框失去焦点
  handleBlur() {
    this.setData({
      isFocused: false
    });
  },

  // 发送消息
  sendMessage() {
    // 检查输入是否为空或者是否正在加载中
    if (!this.data.inputValue.trim() || this.data.isLoading) {
      return;
    }

    // 创建用户消息对象
    const userMessage = {
      type: 'user',
      content: this.data.inputValue,
      timestamp: new Date().getTime()
    };

    // 获取当前消息数组长度，用于设置滚动位置
    const messageIndex = this.data.messages.length;

    // 更新状态：添加用户消息，清空输入框，设置加载状态
    this.setData({
      messages: [...this.data.messages, userMessage],
      inputValue: '',
      isLoading: true,
      scrollToView: `msg-${messageIndex}` // 滚动到新消息
    }, () => {
      // 添加轻微震动反馈
      wx.vibrateShort({
        type: 'light'
      });

      // 延迟一下再滚动到底部，确保视图已更新
      setTimeout(() => {
        this.scrollToBottom();
      }, 100);

      // 调用AI接口获取回复
      this.mockAIResponse(userMessage.content)
        .then((response) => {
          // 创建AI回复消息
          const aiMessage = {
            type: 'ai',
            content: response || '抱歉，我现在无法回答这个问题。',
            timestamp: new Date().getTime()
          };

          const result = app.towxml(aiMessage.content, 'markdown');

          // 更新状态
          this.setData({
            messages: [...this.data.messages, aiMessage],
            result: result,
            isLoading: false,
            lastMessageTime: new Date().getTime(),
            scrollToView: `msg-${messageIndex + 1}` // 滚动到AI回复
          }, () => {
            // 延迟一下再滚动到底部，确保视图已更新
            setTimeout(() => {
              this.scrollToBottom();
            }, 100);
          });
        })
        .catch(error => {
          // 处理错误情况
          console.error('AI响应错误:', error);

          const errorMessage = {
            type: 'ai',
            content: '抱歉，发生了错误，请稍后再试。',
            timestamp: new Date().getTime()
          };

          const errorResult = app.towxml(errorMessage.content, 'markdown');

          // 更新状态
          this.setData({
            messages: [...this.data.messages, errorMessage],
            result: errorResult,
            isLoading: false,
            scrollToView: `msg-${messageIndex + 1}`
          }, () => {
            this.scrollToBottom();
          });
        });
    });
  },

  mockAIResponse(question) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        this.realAIResponse(question).then((response) => {
          resolve(response);
        }).catch((error) => {
          reject(error);
        });
      }, 1000);
    });
  },

  // realAIResponse(question) {
  //     //传输
  //     const url = '/qa/askAI';
  //     const data = {
  //         question: `qustion=${encodeURIComponent(question)}`
  //     };
  //     console.log('question',data.question)
  //     const config = {
  //         header: {
  //             'Content-Type': 'application/x-www-form-urlencoded',
  //             'Authorization': 'eyJhbGciOiJIUzUxMiJ9.eyJvcGVuSWQiOiJvSzFlcTY2ZzNWUDFCc1Z5bXVyNk54ZlF6N3M0IiwiZXhwIjoxNzM3NTQ2MzA2fQ.XzwNb1JU4rh3UXzAKV5AnXxdyg2KcYE5XSAlPelGgd3wHYqBWUyZ0i3ydfWswKD28ROKjUMP648RF_TIwc4uSg'
  //         }
  //     };
  //     const header = config.header
  //     //请求

  //     return new Promise((resolve, reject) => {
  //         request.post(url, data, header,
  //             (res) => {
  //                 console.log('success',res)
  //                 resolve(res.data.data);
  //             }, (err) => {
  //                 console.error('调用AI接口出错:', err);
  //                 reject(err)
  //             })
  //     });
  // }
  //直接请求wx.request
  realAIResponse(question) {
    console.log('发送问题:', question);
    const url = api.AskGPT();
    const data = {
      "question": `qustion=${encodeURIComponent(question)}`
    };
    const header = {
      "content-type": 'application/x-www-form-urlencoded',
      "Authorization": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJvcGVuSWQiOiJvSzFlcTY4aUFnTUQyR1NXOGtkZF9rU0lVc3lvIiwiZXhwIjoxNzQ1NDc0NzAxfQ.xCTM8u0Vz_Im7WFBV5Yow2ykbuomS69N3KR_hW-wa4z87HGIXc-6mlBZw5HTF2Bk9e4cf9vdoZRw3LORE7gIQA"
    };

    return new Promise((resolve, reject) => {
      wx.request({
        url: url,
        method: 'POST',
        header: header,
        data: data,
        success: (res) => {
          console.log('AI响应成功:', res);
          // 检查响应是否有效
          if (res.statusCode === 200 && res.data && res.data.data) {
            resolve(res.data.data);
          } else {
            // 如果响应无效，返回一个友好的错误消息
            const errorMsg = '抱歉，我暂时无法回答这个问题。';
            console.error('无效的API响应:', res);
            resolve(errorMsg); // 使用resolve而不是reject，以便显示友好消息
          }
        },
        fail: (err) => {
          console.error('调用AI接口出错:', err);
          reject(err);
        }
      });
    });
  },

  // 滚动到底部
  scrollToBottom() {
    const query = wx.createSelectorQuery();
    query.select('.message-scroll-view').boundingClientRect();
    query.selectViewport().scrollOffset();
    query.exec((res) => {
      if (res && res[0] && res[1]) {
        const scrollHeight = res[0].height;
        const scrollTop = res[1].scrollTop;

        // 使用新的 API 获取窗口信息
        let windowHeight = 667; // 默认值
        try {
          if (wx.getWindowInfo) {
            windowHeight = wx.getWindowInfo().windowHeight;
          } else {
            windowHeight = wx.getSystemInfoSync().windowHeight;
          }
        } catch (error) {
          console.error('获取窗口信息失败:', error);
        }

        wx.pageScrollTo({
          scrollTop: scrollHeight - windowHeight + 100,
          duration: 300
        });
      }
    });
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'AI智能助手 - 随时随地解答您的问题',
      path: '/pages/gpt/gpt',
      imageUrl: '/img/tabar_icon/彩<EMAIL>'
    };
  }
});