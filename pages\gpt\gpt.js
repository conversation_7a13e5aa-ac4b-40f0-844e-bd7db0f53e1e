const request = require('../../utils/request');
const api=require('../../utils/API')
const app=getApp()
Page({
  data: {
    messages: [],
    inputValue: '',
    question: '',
    isFocused: false,
    scrollToView: '',
    isLoading: false
  },

  onLoad() {
    // 添加欢迎消息
    this.addWelcomeMessage();
  },

  addWelcomeMessage() {
    const welcomeMessage = {
      type: 'ai',
      content: '👋 你好！我是安全智解ai助手，有什么我可以帮助你的吗？'
    };

    const result = app.towxml(welcomeMessage.content, 'markdown');

    this.setData({
      messages: [welcomeMessage],
      result: result
    });
  },

  handleInput(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  // 处理输入框获取焦点
  handleFocus() {
    this.setData({
      isFocused: true
    });
  },

  // 处理输入框失去焦点
  handleBlur() {
    this.setData({
      isFocused: false
    });
  },

  sendMessage() {
    // 检查输入是否为空或者是否正在加载中
    if (!this.data.inputValue.trim() || this.data.isLoading) {
      return;
    }

    // 创建用户消息对象
    const userMessage = {
      type: 'user',
      content: this.data.inputValue
    };

    // 获取当前消息数组长度，用于设置滚动位置
    const messageIndex = this.data.messages.length;

    // 更新状态：添加用户消息，清空输入框，设置加载状态
    this.setData({
      messages: this.data.messages.concat(userMessage),
      inputValue: '',
      isLoading: true,
      scrollToView: `msg-${messageIndex}` // 滚动到新消息
    }, () => {
      // 添加一个临时的"正在输入"消息
      const loadingMessage = {
        type: 'ai',
        content: '正在思考...'
      };

      const loadingResult = app.towxml(loadingMessage.content, 'markdown');

      // 添加加载消息
      this.setData({
        messages: this.data.messages.concat(loadingMessage),
        result: loadingResult,
        scrollToView: `msg-${messageIndex + 1}` // 滚动到加载消息
      });

      // 调用AI接口获取回复
      this.mockAIResponse(userMessage.content).then((response) => {
        // 创建AI回复消息
        const aiMessage = {
          type: 'ai',
          content: response || '抱歉，我现在无法回答这个问题。'
        };

        const result = app.towxml(aiMessage.content, 'markdown');

        // 替换加载消息为实际回复
        const updatedMessages = [...this.data.messages];
        updatedMessages[updatedMessages.length - 1] = aiMessage;

        // 更新状态
        this.setData({
          messages: updatedMessages,
          result: result,
          isLoading: false,
          scrollToView: `msg-${messageIndex + 1}` // 滚动到AI回复
        });
      }).catch(error => {
        // 处理错误情况
        const errorMessage = {
          type: 'ai',
          content: '抱歉，发生了错误，请稍后再试。'
        };

        const errorResult = app.towxml(errorMessage.content, 'markdown');

        // 替换加载消息为错误消息
        const updatedMessages = [...this.data.messages];
        updatedMessages[updatedMessages.length - 1] = errorMessage;

        // 更新状态
        this.setData({
          messages: updatedMessages,
          result: errorResult,
          isLoading: false
        });
      });
    });
  },

  mockAIResponse(question) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        this.realAIResponse(question).then((response) => {
          resolve(response);
        }).catch((error) => {
          reject(error);
        });
      }, 1000);
    });
  },

  // realAIResponse(question) {
  //     //传输
  //     const url = '/qa/askAI';
  //     const data = {
  //         question: `qustion=${encodeURIComponent(question)}`
  //     };
  //     console.log('question',data.question)
  //     const config = {
  //         header: {
  //             'Content-Type': 'application/x-www-form-urlencoded',
  //             'Authorization': 'eyJhbGciOiJIUzUxMiJ9.eyJvcGVuSWQiOiJvSzFlcTY2ZzNWUDFCc1Z5bXVyNk54ZlF6N3M0IiwiZXhwIjoxNzM3NTQ2MzA2fQ.XzwNb1JU4rh3UXzAKV5AnXxdyg2KcYE5XSAlPelGgd3wHYqBWUyZ0i3ydfWswKD28ROKjUMP648RF_TIwc4uSg'
  //         }
  //     };
  //     const header = config.header
  //     //请求

  //     return new Promise((resolve, reject) => {
  //         request.post(url, data, header,
  //             (res) => {
  //                 console.log('success',res)
  //                 resolve(res.data.data);
  //             }, (err) => {
  //                 console.error('调用AI接口出错:', err);
  //                 reject(err)
  //             })
  //     });
  // }
  //直接请求wx.request
  realAIResponse(question) {
    console.log('question',question)
    const url = api.AskGPT();
    const data = {
      "question":`qustion=${encodeURIComponent(question)}`
      // "question":question
    };
    const header={
      "content-type":'application/x-www-form-urlencoded',
      "Authorization":"Bearer eyJhbGciOiJIUzUxMiJ9.eyJvcGVuSWQiOiJvSzFlcTY4aUFnTUQyR1NXOGtkZF9rU0lVc3lvIiwiZXhwIjoxNzQ1NDc0NzAxfQ.xCTM8u0Vz_Im7WFBV5Yow2ykbuomS69N3KR_hW-wa4z87HGIXc-6mlBZw5HTF2Bk9e4cf9vdoZRw3LORE7gIQA"
    };
    return new Promise((resolve, reject) => {
      wx.request({
        url: url,
        method: 'POST',
        header: header,
        data:data,
        success: (res) => {
          console.log('success in ai', res)
        },
        fail: (err) => {
          console.error('调用AI接口出错:', err);
          reject(err);
        }
      });
    });
  },

  // 不再需要scrollToBottom方法，因为我们使用了scroll-into-view属性
  // 但为了兼容性，我们保留这个方法
  scrollToBottom() {
    if (this.data.messages.length === 0) return;

    const lastIndex = this.data.messages.length - 1;
    this.setData({
      scrollToView: `msg-${lastIndex}`
    });
  }
});