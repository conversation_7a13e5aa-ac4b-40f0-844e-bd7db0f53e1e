/* pages/gpt/gpt.wxss */
page {
    background-color: #f0f2f5; /* 更现代的浅灰背景 */
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 顶部标题栏 */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 100rpx;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    z-index: 100;
}

.header-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
}

/* 设置整体视图的样式 */
.all {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f0f2f5;
}

/* 聊天容器样式 */
.chat-container {
    flex: 1;
    background-color: #f0f2f5;
    padding: 120rpx 30rpx 140rpx 30rpx; /* 上右下左 - 留出头部和底部的空间 */
    overflow-y: auto;
    box-sizing: border-box;
}

/* 单个消息样式 */
.message {
    margin-bottom: 30rpx;
    display: flex;
    flex-direction: column;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10rpx); }
    to { opacity: 1; transform: translateY(0); }
}

/* AI消息头部样式 */
.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;
}

/* AI头像样式 */
.ai-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #2563eb; /* 蓝色背景 */
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
    margin-right: 12rpx;
}

/* 消息内容样式 - 基础样式 */
.message-content {
    padding: 20rpx 24rpx;
    border-radius: 18rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    line-height: 1.6;
    font-size: 30rpx;
    position: relative;
    max-width: 70%; /* 限制最大宽度 */
}

/* 用户消息样式 */
.user-message {
    align-items: flex-end;
}

/* 用户消息内容样式 */
.user-message .message-content {
    background-color: #2563eb; /* 蓝色背景 */
    color: white;
    margin-left: auto; /* 右对齐 */
    border-bottom-right-radius: 4rpx; /* 右下角尖角效果 */
}

.user-message .message-content::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: -16rpx;
    width: 0;
    height: 0;
    border: 16rpx solid transparent;
    border-left-color: #2563eb;
    border-right: 0;
    border-bottom: 0;
    margin-right: 0;
    margin-bottom: 0;
}

/* AI消息样式 */
.ai-message {
    align-items: flex-start;
}

/* AI消息内容样式 */
.ai-message .message-content {
    background-color: white;
    color: #333;
    margin-right: auto; /* 左对齐 */
    border-bottom-left-radius: 4rpx; /* 左下角尖角效果 */
    margin-left: 92rpx; /* 为头像留出空间 */
}

.ai-message .message-content::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: -16rpx;
    width: 0;
    height: 0;
    border: 16rpx solid transparent;
    border-right-color: white;
    border-left: 0;
    border-bottom: 0;
    margin-left: 0;
    margin-bottom: 0;
}

/* 输入容器样式 */
.input-container {
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;
    background-color: white;
    position: fixed; /* 固定位置 */
    bottom: 0; /* 固定在底部 */
    left: 0;
    right: 0;
    width: 100%; /* 宽度占满父元素 */
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    box-sizing: border-box;
    z-index: 100;
}

/* 输入字段容器 */
.input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 36rpx;
    padding: 0 24rpx;
    margin-right: 20rpx;
    transition: all 0.3s ease;
}

.input-wrapper.focused {
    background-color: #f0f0f0;
    box-shadow: 0 0 0 2rpx rgba(37, 99, 235, 0.2);
}

/* 输入字段样式 */
.input-field {
    flex: 1;
    border: none;
    background: transparent;
    padding: 16rpx 0;
    font-size: 30rpx;
    color: #333;
}

/* 发送按钮样式 */
.send-button {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #2563eb;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.send-button:active {
    transform: scale(0.95);
    background-color: #1d4ed8;
}

/* 发送图标样式 */
.send-icon {
    width: 40rpx;
    height: 40rpx;
}
