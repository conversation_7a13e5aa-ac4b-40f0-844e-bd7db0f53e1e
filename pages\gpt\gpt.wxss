/* pages/gpt/gpt.wxss */
page {
    background-color: #f5f7fa; /* 更现代的浅灰背景 */
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: #333;
    --primary-color: #4e89fd;
    --primary-light: #e8f0ff;
    --primary-dark: #3b7af9;
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #f5222d;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-tertiary: #999999;
    --background-color: #f5f7fa;
    --card-background: #ffffff;
    --border-radius-sm: 8rpx;
    --border-radius-md: 16rpx;
    --border-radius-lg: 24rpx;
    --border-radius-xl: 32rpx;
    --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --spacing-xs: 8rpx;
    --spacing-sm: 16rpx;
    --spacing-md: 24rpx;
    --spacing-lg: 32rpx;
    --spacing-xl: 48rpx;
}

/* 页面头部样式 */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 100rpx;
    background-color: var(--card-background);
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 var(--spacing-lg);
}

.header-title {
    font-size: 36rpx;
    font-weight: 600;
    color: var(--text-primary);
}

.header-actions {
    display: flex;
    align-items: center;
}

.header-action-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.header-action-btn:active {
    background-color: rgba(0, 0, 0, 0.05);
}

.header-action-icon {
    width: 40rpx;
    height: 40rpx;
}

/* 聊天容器样式 */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--background-color);
    padding-top: 100rpx; /* 为头部留出空间 */
    padding-bottom: 140rpx; /* 为输入区域留出空间 */
    box-sizing: border-box;
}

.message-scroll-view {
    flex: 1;
    padding: var(--spacing-lg);
}

/* 欢迎部分样式 */
.welcome-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl) 0;
    margin: 60rpx 0;
}

.welcome-logo {
    width: 160rpx;
    height: 160rpx;
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-lg);
    background-color: var(--primary-light);
    padding: var(--spacing-md);
}

.welcome-title {
    font-size: 40rpx;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.welcome-subtitle {
    font-size: 28rpx;
    color: var(--text-tertiary);
}

/* 单个消息样式 */
.message {
    margin-bottom: var(--spacing-xl);
    position: relative;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10rpx); }
    to { opacity: 1; transform: translateY(0); }
}

/* AI消息头部样式 */
.message-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.ai-info {
    margin-left: var(--spacing-sm);
}

.ai-name {
    font-size: 26rpx;
    color: var(--text-secondary);
    font-weight: 500;
}

/* AI头像样式 */
.ai-avatar {
    width: 70rpx;
    height: 70rpx;
    border-radius: var(--border-radius-lg);
    background-color: var(--primary-light);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

/* 消息内容基础样式 */
.message-content {
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    font-size: 28rpx;
    line-height: 1.6;
    position: relative;
    max-width: 80%;
    word-wrap: break-word;
}

/* 用户消息样式 */
.user-message {
    display: flex;
    justify-content: flex-end;
}

.user-content {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    margin-left: auto;
    border-top-right-radius: var(--border-radius-sm);
    box-shadow: 0 4rpx 16rpx rgba(78, 137, 253, 0.2);
}

.user-content::after {
    content: "";
    position: absolute;
    top: 20rpx;
    right: -16rpx;
    width: 0;
    height: 0;
    border: 16rpx solid transparent;
    border-left-color: var(--primary-dark);
    border-right: 0;
    border-top: 0;
}

/* AI消息样式 */
.ai-message {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.ai-content {
    background-color: var(--card-background);
    color: var(--text-primary);
    margin-right: auto;
    border-top-left-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-sm);
}

.ai-content::after {
    content: "";
    position: absolute;
    top: 20rpx;
    left: -16rpx;
    width: 0;
    height: 0;
    border: 16rpx solid transparent;
    border-right-color: var(--card-background);
    border-left: 0;
    border-top: 0;
}

/* 加载指示器样式 */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.loading-dot {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background-color: var(--primary-color);
    margin: 0 8rpx;
    animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* 输入区域样式 */
.input-area {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--card-background);
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    z-index: 99;
}

.input-container {
    display: flex;
    align-items: center;
    background-color: var(--background-color);
    border-radius: 40rpx;
    padding: 0 var(--spacing-md) 0 var(--spacing-lg);
    transition: var(--transition-normal);
    border: 2rpx solid transparent;
}

.input-focused {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4rpx rgba(78, 137, 253, 0.1);
}

.input-field {
    flex: 1;
    height: 80rpx;
    border: none;
    background: transparent;
    font-size: 28rpx;
    color: var(--text-primary);
}

.send-button {
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    background-color: var(--background-color);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    opacity: 0.5;
}

.send-button.active {
    background-color: var(--primary-color);
    opacity: 1;
}

.send-button.active:active {
    transform: scale(0.95);
    background-color: var(--primary-dark);
}

.send-icon {
    width: 36rpx;
    height: 36rpx;
}
