<!-- 搜索框 -->
<view class="searchbox">
  <image class="searchimage" src="http://8.137.94.207/wp-content/uploads/2025/01/search.png" bindtap="onSearchClick" />
  <input
    class="searchinput"
    type="text"
    placeholder="搜索"
    bindinput="onSearchInput"
    value="{{searchQuery}}"
    confirm-type="search"
  />
  <view class="logo" bindtap="onLogoClick">logo</view>
</view>

<!-- 分类栏 -->
<view class="category-nav">
  <block wx:for="{{categories}}" wx:key="id">
    <td-tag
      class="category-item {{categoryId === item.id ? 'active' : ''}}"
      bindtap="onCategoryChange"
      data-id="{{item.id}}"
      variant="{{categoryId === item.id ? 'solid' : 'outline'}}"
    >
      {{item.name}}
    </td-tag>
  </block>
</view>

<!-- 文章列表 -->
<view class="article-list">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class=".no-articles">
    <td-loading />
    <text>正在加载中...</text>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{!loading && articles.length === 0}}" class="no-articles">
    <text>暂无相关文章</text>
  </view>

  <!-- 文章项 -->
  <block wx:for="{{articles}}" wx:key="id" wx:if="{{articles.length > 0}}">
    <view class="article-item" bindtap="onArticleTap" data-id="{{item.id}}">
      <view class="article-content">
        <rich-text class="article-title" nodes="{{item.title}}"></rich-text>
        <view class="article-meta">
          <text class="author">{{item.createBy}}</text>
          <text class="time">{{item.createTime}}</text>
          <text class="favorites">★ {{item.favorites}}</text>
      <!--<text class="favorites">id: {{item.categoryId}}</text>-->
           <text class="favorites">★ {{item.favorites}}</text>
        </view>
      </view>
      <image
        class="article-image"
        src="{{item.image}}"
        mode="aspectFill"
        binderror="onImageError"
        data-index="{{index}}"
      />
    </view>
  </block>
</view>

<!-- 现代化分页组件 -->
<view class="pagination-container" wx:if="{{usepage && totalPages > 1}}">
  <view class="pagination-wrapper">
    <!-- 上一页按钮 -->
    <view
      class="pagination-arrow {{currentPage <= 1 ? 'disabled' : ''}}"
      bindtap="{{currentPage > 1 ? 'onPageChange' : ''}}"
      data-page="{{currentPage - 1}}"
    >
      <view class="pagination-icon pagination-prev"></view>
    </view>

    <!-- 页码按钮组 -->
    <view class="pagination-numbers">
      <!-- 首页按钮 (当前页大于3时显示) -->
      <view
        wx:if="{{currentPage > 3}}"
        class="pagination-number"
        bindtap="onPageChange"
        data-page="1"
      >1</view>

      <!-- 省略号 (当前页大于4时显示) -->
      <view wx:if="{{currentPage > 4}}" class="pagination-ellipsis">...</view>

      <!-- 当前页前一页 -->
      <view
        wx:if="{{currentPage > 1}}"
        class="pagination-number"
        bindtap="onPageChange"
        data-page="{{currentPage - 1}}"
      >{{currentPage - 1}}</view>

      <!-- 当前页 -->
      <view class="pagination-number current">{{currentPage}}</view>

      <!-- 当前页后一页 -->
      <view
        wx:if="{{currentPage < totalPages}}"
        class="pagination-number"
        bindtap="onPageChange"
        data-page="{{currentPage + 1}}"
      >{{currentPage + 1}}</view>

      <!-- 省略号 (当前页小于总页数-3时显示) -->
      <view wx:if="{{currentPage < totalPages - 3}}" class="pagination-ellipsis">...</view>

      <!-- 尾页按钮 (当前页小于总页数-2时显示) -->
      <view
        wx:if="{{currentPage < totalPages - 2}}"
        class="pagination-number"
        bindtap="onPageChange"
        data-page="{{totalPages}}"
      >{{totalPages}}</view>
    </view>

    <!-- 下一页按钮 -->
    <view
      class="pagination-arrow {{currentPage >= totalPages ? 'disabled' : ''}}"
      bindtap="{{currentPage < totalPages ? 'onPageChange' : ''}}"
      data-page="{{currentPage + 1}}"
    >
      <view class="pagination-icon pagination-next"></view>
    </view>
  </view>

  <!-- 页码信息 -->
  <view class="pagination-info">
    <text>{{currentPage}} / {{totalPages}} 页</text>
  </view>
</view>
