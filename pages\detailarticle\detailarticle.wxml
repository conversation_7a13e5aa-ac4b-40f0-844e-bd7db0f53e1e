<!-- 搜索框 -->
<view class="searchbox">
  <image class="searchimage" src="http://8.137.94.207/wp-content/uploads/2025/01/search.png" bindtap="onSearchClick" />
  <input 
    class="searchinput" 
    type="text" 
    placeholder="搜索" 
    bindinput="onSearchInput" 
    value="{{searchQuery}}" 
    confirm-type="search"
  />
  <view class="logo" bindtap="onLogoClick">logo</view>
</view>

<!-- 分类栏 -->
<view class="category-nav">
  <block wx:for="{{categories}}" wx:key="id">
    <td-tag 
      class="category-item {{categoryId === item.id ? 'active' : ''}}"
      bindtap="onCategoryChange" 
      data-id="{{item.id}}"
      variant="{{categoryId === item.id ? 'solid' : 'outline'}}"
    >
      {{item.name}}
    </td-tag>
  </block>
</view>

<!-- 文章列表 -->
<view class="article-list">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class=".no-articles">
    <td-loading />
    <text>正在加载中...</text>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{!loading && articles.length === 0}}" class="no-articles">
    <text>暂无相关文章</text>
  </view>

  <!-- 文章项 -->
  <block wx:for="{{articles}}" wx:key="id" wx:if="{{articles.length > 0}}">
    <view class="article-item" bindtap="onArticleTap" data-id="{{item.id}}">
      <view class="article-content">
        <rich-text class="article-title" nodes="{{item.title}}"></rich-text>
        <view class="article-meta">
          <text class="author">{{item.createBy}}</text>
          <text class="time">{{item.createTime}}</text>
          <text class="favorites">★ {{item.favorites}}</text>
      <!--<text class="favorites">id: {{item.categoryId}}</text>-->
           <text class="favorites">★ {{item.favorites}}</text>
        </view>
      </view>
      <image 
        class="article-image" 
        src="{{item.image}}" 
        mode="aspectFill" 
        binderror="onImageError" 
        data-index="{{index}}"
      />
    </view>
  </block>
</view>

<!-- 分页器 -->
<view class="pagination" wx:if="{{usepage && totalPages > 1}}">
  <td-button 
    wx:if="{{currentPage > 1}}"
    bindtap="onPageChange" 
    data-page="1"
    size="small"
  >首页</td-button>

  <block wx:for="{{pageNumbers.left}}" wx:key="*this">
    <td-button 
      bindtap="onPageChange" 
      data-page="{{item}}"s
      size="small"
    >{{item}}</td-button>
  </block>

  <td-button type="primary" size="small">{{currentPage}}</td-button>

  <block wx:for="{{pageNumbers.right}}" wx:key="*this">
    <td-button 
      bindtap="onPageChange" 
      data-page="{{item}}"
      size="small"
    >{{item}}</td-button>
  </block>

  <td-button 
    wx:if="{{currentPage < totalPages}}"
    bindtap="onPageChange" 
    data-page="{{totalPages}}"
    size="small"
  >尾页</td-button>
</view>
<!-- 页码和翻页按钮区域 -->

<view wx:if="{{usepage}}" class="pagination">
  <button class="first-page-button" data-page="" bindtap="onPageChange">首页</button>
  <!-- 往前的翻页按钮 -->
  <view wx:if="{{currentPage > 2 && !loading}}">
    <button class="up-up-button" data-page="{{currentPage - 2}}" bindtap="onPageChange">
      {{currentPage - 2}}
    </button>
  </view>
  <view wx:else>
    <button class="up-up-button" style="visibility: hidden;">
      {{currentPage > 2 ? currentPage - 2 : ''}}
    </button>
  </view>

  <view wx:if="{{currentPage > 1 && !loading}}">
    <button class="up-button" data-page="{{currentPage - 1}}" bindtap="onPageChange">
      {{currentPage - 1}}
    </button>
  </view>
  <view wx:else>
    <button class="up-button" style="visibility: hidden;">
      {{currentPage > 1 ? currentPage - 1 : ''}}
    </button>
  </view>

  <view class="middle-button">
    <button class="pagination-button">{{currentPage}}</button>
  </view>
<!-- 往后的翻页按钮-->
  <view wx:if="{{currentPage < totalPages && !loading}}">
    <button class="next-button" data-page="{{currentPage + 1}}" bindtap="onPageChange">
      {{currentPage + 1}}
    </button>
  </view>
  <view wx:else>
    <button class="next-button" style="visibility: hidden;">
      {{currentPage < totalPages ? currentPage + 1 : ''}}
    </button>
  </view>

  <view wx:if="{{currentPage < totalPages - 1 && !loading}}">
    <button class="next-next-button" data-page="{{currentPage + 2}}" bindtap="onPageChange">
      {{currentPage + 2}}
    </button>
  </view>
  <view wx:else>
    <button class="next-next-button" style="visibility: hidden;">
      {{currentPage < totalPages - 1 ? currentPage + 2 : ''}}
    </button>
  </view>

  <button class="last-page-button" data-page="{{totalPages}}" bindtap="onPageChange">尾页</button>
</view>
