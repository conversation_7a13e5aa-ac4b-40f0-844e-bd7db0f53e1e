# 微信小程序相关
.DS_Store
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
Thumbs.db
ehthumbs.db
Desktop.ini

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp

# 缓存文件
.cache/
.npm/
.yarn/

# 构建输出
dist/
build/

# 环境配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 微信开发者工具生成的文件
project.private.config.json

# 依赖包
package-lock.json
yarn.lock

# 测试覆盖率
coverage/

# 其他
*.zip
*.tar.gz
*.rar
