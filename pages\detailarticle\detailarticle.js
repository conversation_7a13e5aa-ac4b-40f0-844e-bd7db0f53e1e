const article = require('../../utils/post');
const app = getApp();

Page({
  data: {
    categories: [],
    selectedCategoryId: -1,
    param: undefined,
    articles: [],
    allArticles: [],
    loading: false,
    currentPage: 1,
    pageSize: 5,
    totalPages: 0,
    pageNumbers: { left: [], right: [] },
    categoryIdToName: {
      '-1': '全部',
      '1': '行业透视',
      '2': '职业需求',
      '3': '网安学涯',
      '4': '行业认证',
      '5': '信创科普',
      '6': '赛事信息',
      '7': '研学培训',
      '8': '网安高校',
      '9': '法律法规',
      '10': '行业报告',
      '11': '攻击与防御系统',
      '12': '网络安全防护体系',
      '13': '网络安全理论基础',
      '14': '新兴技术与安全挑战',
      '15': '网络科普短视频',
      '16': '网安法律法规',
      '17': '网络安全研究与趋势',
      '18': '网络安全新闻'
    },
    usepage: true,
    useTestData: false,
    searchText: '',
  },

  onLoad(options) {
    this.setData({
      param: options.param || this.data.param,
      title: options.title || this.data.title,
      selectedCategoryId: options.categoryId ? Number(options.categoryId) : -1, // 处理 categoryId
      useTestData: options.useTestData === 'true' ? true : this.data.useTestData,
    });

    wx.setNavigationBarTitle({
      title: options.title ? `文章分类: ${options.title}` : '默认标题',
    });

    this.fetchArticles(true);
    this.updatePageNumbers();
  },

  //获取文章，关键函数
  fetchArticles(refresh = false) {
    const { param, currentPage, pageSize, selectedCategoryId, useTestData } = this.data;

    if (refresh) {
      this.setData({
        currentPage: 1,
        articles: [],
        allArticles: [],
        loading: true,
      });
    }

    if (useTestData) {
      // 测试数据（确保数组正确定义）
      const testData = [
        {
          id: 1,
          title: '测试文章1：探索未来科技',
          categoryId: 1,
          image: 'https://example.com/test1.jpg',
          createBy: '作者A',
          createTime: '2025-03-26 09:00',
          favorites: 15,
        },
        {
          id: 2,
          title: '测试文章2：前端开发技巧',
          categoryId: 2,
          image: 'https://example.com/test2.jpg',
          createBy: '作者B',
          createTime: '2025-03-26 09:30',
          favorites: 23,
        },
        {
          id: 3,
          title: '测试文章3：微信小程序开发',
          categoryId: 1,
          image: 'https://example.com/test3.jpg',
          createBy: '作者C',
          createTime: '2025-03-26 10:00',
          favorites: 42,
        },
        {
          id: 4,
          title: '测试文章4：移动端UI设计',
          categoryId: 4,
          image: 'https://example.com/test4.jpg',
          createBy: '作者D',
          createTime: '2025-03-26 10:30',
          favorites: 8,
        },
        {
          id: 5,
          title: '测试文章5：数据可视化实践',
          categoryId: 1,
          image: 'https://example.com/test5.jpg',
          createBy: '作者E',
          createTime: '2025-03-26 11:00',
          favorites: 30,
        },
      ];

      const allArticles = [...testData];
      console.log('测试数据验证:', Array.isArray(allArticles)); // 应输出true

      // 分类处理
      const tags = Array.from(new Set(allArticles.map(item => item.categoryId)))
        .sort((a, b) => a - b)
        .filter(id => !isNaN(id)); // 过滤非数字ID

      const categories = [
        { id: -1, name: this.data.categoryIdToName[-1] },
        ...tags.map(tag => ({
          id: tag,
          name: this.data.categoryIdToName[tag] || `其他分类(${tag})`
        }))
      ];

      this.setData({
        categories: categories,
        allArticles: allArticles,
        totalPages: Math.ceil(allArticles.length / pageSize),
      });

      // 图片检测逻辑
      Promise.all(allArticles.map((item, index) => {
        const imageUrl = item.image;
        return this.checkImageValid(imageUrl).then(isValid => {
          if (!isValid) {
            console.log(`图片[${index}]无效: ${imageUrl}`);
            item.image = 'https://pic.616pic.com/ys_bnew_img/00/13/31/lijf1cY7MX.jpg';
          }
          return item;
        });
      })).then((articlesWithValidImages) => {
        this.setData({
          articles: articlesWithValidImages.slice((currentPage - 1) * pageSize, currentPage * pageSize),
          loading: false,
        });
        this.updatePageNumbers();
      });

    } else {
      // 真实数据逻辑（新增错误处理）
      if (!param) {
        wx.showToast({ title: '标签错误', icon: 'none' });
        this.setData({ loading: false });
        return;
      }

      const fetchList = () => {
        let requestPromises = [];
        if (selectedCategoryId === -1) {
            requestPromises.push(article.list(param, null));
        } else {
          requestPromises.push(article.list(param, selectedCategoryId));
        }

        Promise.all(requestPromises)
          .then((results) => {
            // 新增数组有效性校验
            const allArticles = results.flat().filter(Boolean);
            if (!Array.isArray(allArticles)) {
              throw new Error('API返回数据格式错误');
            }

            // 分类处理
            const tags = Array.from(new Set(allArticles.map(item => item.categoryId)))
              .sort((a, b) => a - b)
              .filter(id => !isNaN(id));

            const categories = [
              { id: "-1", name: this.data.categoryIdToName[-1] },
              ...tags.map(tag => ({
                id: String(tag),
                name: this.data.categoryIdToName[tag] || `其他分类(${tag})`
              }))
            ];

            this.setData({
              categories: categories,
              allArticles: allArticles,
              totalPages: Math.ceil(allArticles.length / pageSize),
            });

            // 图片检测
            Promise.all(allArticles.map((item, index) => {
              const imageUrl = item.image;
              return this.checkImageValid(imageUrl).then(isValid => {
                if (!isValid) {
                //  console.log(`图片[${index}]无效: ${imageUrl}`);
                  item.image = 'https://pic.616pic.com/ys_bnew_img/00/13/31/lijf1cY7MX.jpg';
                }
                return item;
              });
            })).then((articlesWithValidImages) => {
              this.setData({
                articles: articlesWithValidImages.slice((currentPage - 1) * pageSize, currentPage * pageSize),
                loading: false,
              });
              this.updatePageNumbers();
            });
          })
          .catch((err) => {
            console.error('加载失败:', err);
            this.setData({ loading: false });
            wx.showToast({ title: '数据加载失败', icon: 'none' });
          });
      };

      fetchList();
    }
  },


  filterArticlesByCategory(categoryId, page = 1) {//筛选对应板块的文章列表
    const { allArticles, pageSize } = this.data;
    const filteredArticles = categoryId === -1
      ? allArticles
      : allArticles.filter(item => item.categoryId === categoryId);
    const totalPages = Math.ceil(filteredArticles.length / pageSize);
    const start = (page - 1) * pageSize;
    const currentArticles = filteredArticles.slice(start, start + pageSize);

    this.setData({
      articles: currentArticles,
      totalPages,
      currentPage: page,
      loading: false,
    });
    this.updatePageNumbers();
  },

  onCategoryChange(event) {//点击分类导航栏触发的函数
    const categoryId = Number(event.currentTarget.dataset.id);
     this.filterArticlesByCategory(categoryId);
    this.setData({
      selectedCategoryId: categoryId,
      currentPage: 1,
    });

  },

/**
 * 页码切换处理函数
 * 处理用户点击分页组件时的页面切换
 */
onPageChange(event) {
  // 获取目标页码并进行有效性验证
  let page = Number(event.currentTarget.dataset.page);

  // 页码有效性检查
  if (page == null || isNaN(page) || page <= 0) {
    page = 1;
  }

  // 页码范围检查
  if (page < 1) {
    page = 1;
  } else if (page > this.data.totalPages) {
    page = this.data.totalPages;
  }

  // 如果点击的是当前页，不做任何操作
  if (page === this.data.currentPage) {
    return;
  }

  // 更新当前页码
  this.setData({
    currentPage: page,
    loading: true // 添加加载状态
  });

  // 添加页面切换反馈
  wx.vibrateShort({
    type: 'light'
  });

  // 根据是否有搜索关键词决定使用哪种分页逻辑
  if (this.data.searchText.trim()) {
    // 搜索结果分页
    const filtered = this.data.allArticles.filter(item =>
      item.title.includes(this.data.searchText)
    );

    const start = (page - 1) * this.data.pageSize;
    const end = page * this.data.pageSize;

    this.setData({
      articles: filtered.slice(start, end),
      loading: false
    });

    // 滚动到页面顶部
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
  } else {
    // 普通分类分页
    this.filterArticlesByCategory(this.data.selectedCategoryId, page);

    // 滚动到页面顶部
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
  }
},

/**
 * 更新分页数字显示
 * 根据当前页码和总页数计算应该显示哪些页码
 */
updatePageNumbers() {
  const { currentPage, totalPages } = this.data;

  // 不再需要这个函数的原始逻辑，因为我们的新分页组件
  // 已经在WXML中直接处理了页码显示逻辑
  // 但为了兼容性，我们保留这个函数

  // 仍然更新左右页码数组，以防其他地方依赖
  const leftPages = [];
  const rightPages = [];

  // 计算左侧页码
  if (currentPage > 1) {
    leftPages.push(currentPage - 1);
  }

  // 计算右侧页码
  if (currentPage < totalPages) {
    rightPages.push(currentPage + 1);
  }

  this.setData({
    'pageNumbers.left': leftPages,
    'pageNumbers.right': rightPages,
  });
},

  onReachBottom() {
    wx.showToast({ title: '已经到底了喵~', icon: 'none' });
  },

  onArticleTap(event) {//进入文章详细页
    const articleId = event.currentTarget.dataset.id;
    wx.navigateTo({ url: `/pages/detail/detail?id=${articleId}` });
  },

  checkImageValid(url) {//验证图片url是否有效
    return new Promise((resolve) => {
      wx.request({
        url: url,
        method: 'HEAD',
        success: (res) => resolve(res.statusCode === 200),
        fail: () => resolve(false)
      });
    });
  },

  onSearchInput(event) {
    this.setData({ searchText: event.detail.value });
  },

  onSearchClick() {
    const keyword = this.data.searchText.trim();
    console.log('触发搜索函数');
    if (!keyword) {
      this.setData({
        currentPage: 1,
        articles: this.data.allArticles.slice(0, this.data.pageSize),
        totalPages: Math.ceil(this.data.allArticles.length / this.data.pageSize),
        loading: false,
      });
      return;
    }
    const filtered = this.data.allArticles.filter(item => item.title.includes(keyword));
    this.setData({
      articles: filtered.slice(0, this.data.pageSize),
      totalPages: Math.ceil(filtered.length / this.data.pageSize),
      currentPage: 1,
      loading: false,
    });
  },

  onLogoClick() {
    wx.showToast({ title: '开始搜索了喵', icon: 'none' });
    this.onSearchClick();
  },

  onImageError(event) {
    const index = event.currentTarget.dataset.index;
    const backupUrl = 'https://pic.616pic.com/ys_bnew_img/00/13/31/lijf1cY7MX.jpg';
    const articles = [...this.data.articles];
    if (articles[index]) {
      articles[index].image = backupUrl;
      this.setData({ articles });
    }
  }
});