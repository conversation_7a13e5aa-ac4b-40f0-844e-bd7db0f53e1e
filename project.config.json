{"compileType": "miniprogram", "libVersion": "trial", "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": true, "ignoreUploadUnusedFiles": true}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "packOptions": {"ignore": [], "include": []}, "appid": "wxb009bc93d19d3f4d"}