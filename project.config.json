{"compileType": "miniprogram", "libVersion": "trial", "projectname": "网安智解小程序", "description": "网络安全知识科普小程序", "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": true, "ignoreUploadUnusedFiles": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useIsolateContext": true, "nodeModules": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "enableEngineNative": false, "useStaticServer": true, "checkInvalidKey": true, "bundle": false, "lazyloadPlaceholderEnable": true, "preloadBackgroundData": false, "minify": true, "newFeature": true, "autoAudits": false, "uglifyFileName": false, "uploadWithSourceMap": true, "lazyCodeLoading": "requiredComponents"}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "packOptions": {"ignore": [], "include": []}, "appid": "wxb009bc93d19d3f4d", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"}