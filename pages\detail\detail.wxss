/* pages/detail/detail.wxss */
/* 页面整体样式 */
page {
  /* 优化颜色方案 */
  --primary-color: #3b82f6;
  --primary-light: rgba(59, 130, 246, 0.15);
  --secondary-color: #10b981;
  --accent-color: #8b5cf6;
  --text-primary: #1f2937;
  --text-secondary: #4b5563;
  --text-tertiary: #9ca3af;
  --background: #f9fafb;
  --card-bg: #ffffff;
  --card-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.06);
  --border-radius: 16rpx;
  --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  background-color: var(--background);
  font-family: -apple-system-font, Helvetica Neue, sans-serif;
  min-height: 100vh;
  padding: 32rpx;
  color: var(--text-primary);
  box-sizing: border-box;
}

.page-container {
  padding: 32rpx;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

/* 内容卡片样式 */
.content-card {
  background: var(--card-bg);
  border-radius: 24rpx;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  position: relative;
  transition: var(--transition-normal);
  transform: translateY(0);
}

.content-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

/* 文章标题和元数据样式 */
.article-header {
  padding: 40rpx 40rpx 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.article-title {
  font-size: 40rpx;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: 24rpx;
  position: relative;
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  margin-bottom: 24rpx;
  color: var(--text-tertiary);
  font-size: 24rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-icon {
  color: var(--primary-color);
}

/* 文章内容样式 */
.article-body {
  padding: 40rpx;
  position: relative;
}

.article-body::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(
    180deg,
    var(--primary-color),
    var(--secondary-color)
  );
}

.rich-text-content {
  font-size: 30rpx;
  line-height: 1.8;
  color: var(--text-primary);
  letter-spacing: 0.5rpx;
}

/* 评论区容器样式 */
.comment-card {
  background: var(--card-bg);
  border-radius: 24rpx;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: var(--transition-normal);
  position: relative;
  margin-bottom: 40rpx;
}

/* 评论区标题样式 */
.comment-header {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  gap: 16rpx;
  background: linear-gradient(to right, var(--primary-light), transparent);
}

.comment-header::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 6rpx;
  height: 64rpx;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border-top-left-radius: 24rpx;
}

.comment-icon {
  font-size: 40rpx;
  color: var(--primary-color);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
}

/* 评论表单样式 */
.comment-form {
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  background-color: rgba(59, 130, 246, 0.03);
}

.input-container {
  display: flex;
  align-items: flex-end;
  background: white;
  border-radius: 36rpx;
  padding: 16rpx 24rpx;
  transition: var(--transition-fast);
  box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.05), 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
  border: 1rpx solid rgba(59, 130, 246, 0.2);
  position: relative;
  overflow: hidden;
}

.input-container::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--secondary-color)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.input-container:focus-within {
  box-shadow: 0 0 0 2rpx var(--primary-light), 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
  border-color: var(--primary-color);
  transform: translateY(-2rpx);
}

.input-container:focus-within::before {
  opacity: 1;
}

.comment-input, .reply-input {
  flex: 1;
  font-size: 30rpx;
  min-height: 72rpx;
  padding: 12rpx 0;
  color: var(--text-primary);
  font-weight: 400;
}

.placeholder-style {
  color: var(--text-secondary);
  font-size: 28rpx;
}

.send-button {
  margin-left: 12rpx;
  width: 70rpx;
  height: 70rpx;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
  font-weight: bold;
  position: relative;
  overflow: hidden;
}

.send-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.send-button:active {
  transform: scale(0.92);
  opacity: 0.9;
  box-shadow: 0 2rpx 6rpx rgba(0, 122, 255, 0.15);
}

.send-button:active::after {
  opacity: 1;
}

/* 评论项样式 */
.comment-item {
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 24rpx;
  transition: var(--transition-fast);
  position: relative;
}

.comment-item::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 32rpx;
  right: 32rpx;
  height: 1rpx;
  background: linear-gradient(to right, var(--primary-light), transparent);
  opacity: 0.5;
}

.comment-item:last-child::after {
  display: none;
}

.comment-item:active {
  background: rgba(0, 0, 0, 0.02);
}

.comment-avatar {
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  background-color: var(--primary-light);
  overflow: hidden;
  position: relative;
}

.comment-avatar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  box-shadow: inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
}

.comment-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

/* 评论项头部样式 */
.comment-main .comment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
  padding: 0;
  border: none;
  background: none;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.user-name {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.comment-time {
  font-size: 24rpx;
  color: var(--text-tertiary);
}

.comment-time::before {
  content: "·";
  margin: 0 8rpx;
}

/* 回复按钮样式 */
.reply-button {
  font-size: 26rpx;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  background-color: rgba(59, 130, 246, 0.08);
  transition: all 0.2s ease;
  margin-left: auto;
  gap: 4rpx;
  border: none;
}

.reply-button:active {
  background-color: rgba(59, 130, 246, 0.15);
  transform: scale(0.95);
}

/* 回复表单样式 */
.reply-form {
  margin-top: 12rpx;
  background: white;
  border-radius: 20rpx;
  padding: 16rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(59, 130, 246, 0.2);
  transform: translateY(0);
  position: relative;
  overflow: hidden;
}

/* 回复输入框样式 */
.reply-input {
  width: 100%;
  min-height: 72rpx;
  font-size: 30rpx;
  padding: 16rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
}

.reply-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3rpx rgba(59, 130, 246, 0.15), inset 0 2rpx 6rpx rgba(0, 0, 0, 0.02);
  transform: translateY(-1rpx);
}

/* 回复操作按钮容器样式 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12rpx;
  margin-top: 10rpx;
  align-items: center;
}

/* 取消按钮样式 */
.cancel-btn {
  background: rgba(0, 0, 0, 0.08);
  color: var(--text-secondary);
  border-radius: 24rpx;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  transition: var(--transition-fast);
  font-weight: 500;
  min-height: unset;
  line-height: 1.5;
}

.cancel-btn:active {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(0.96);
}

/* 发送按钮样式 */
.send-btn, .confirm-btn {
  font-size: 24rpx;
  padding: 6rpx 18rpx;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  border-radius: 24rpx;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.2);
  font-weight: 600;
  min-height: unset;
  line-height: 1.5;
}

/* 评论内容样式 */
.comment-content {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  word-wrap: break-word;
  padding: 4rpx 0;
}

/* 添加评论表单样式 */
.add-comment-form {
  margin: 0;
  padding: 32rpx;
  background: var(--card-bg);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 24rpx;
  align-items: flex-end;
  position: sticky;
  bottom: 0;
  box-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.03);
}

/* 添加评论表单的文本域样式 */
.add-comment-form textarea {
  flex: 1;
  min-height: 96rpx;
  padding: 24rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.4;
  transition: all 0.2s ease;
  background: var(--background);
}

.add-comment-form textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

/* 添加评论表单的按钮样式 */
.add-comment-form button {
  width: 160rpx;
  height: 96rpx;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.2);
}

.add-comment-form button:active {
  transform: scale(0.95);
  opacity: 0.9;
}

/* 回复输入框容器样式 */
.reply-input {
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

/* 回复输入框的文本域样式 */
.reply-input textarea {
  width: 100%;
  min-height: 80rpx;
  padding: 24rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.4;
  background: var(--background);
  transition: all 0.2s ease;
}

.reply-input textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 8rpx rgba(0, 123, 255, 0.2);
}

.comment-item {
  padding: 32rpx;
  margin-bottom: 24rpx;
  background: var(--card-bg);
  border-radius: 24rpx;
  box-shadow: var(--card-shadow);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.comment-item:hover {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transform: translateY(-2rpx);
}

.comment-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.sub-comments {
  margin-top: 24rpx;
  border-left: 4rpx solid rgba(59, 130, 246, 0.2);
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: 20rpx;
  padding: 20rpx 24rpx;
  box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.sub-comments::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), transparent);
  pointer-events: none;
}

.sub-comment-item {
  background: var(--card-bg);
  padding: 24rpx;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 0, 0, 0.03);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
  overflow: hidden;
}

.sub-comment-item:last-child {
  margin-bottom: 0;
}

.sub-comment-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

.sub-comment-avatar {
  flex-shrink: 0;
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  background-color: var(--primary-light);
  overflow: hidden;
}

.author-date-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.comment-author {
  font-weight: 500;
  color: #333;
  font-size: 28rpx;
}

.comment-date {
  font-size: 24rpx;
  color: #999;
}

.reply-input button {
  margin: 10rpx 0 0 10rpx;
  width: 150rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: var(--primary-color);
  color: white;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.reply-input button[type="cancel"] {
  background: #f0f0f0;
  color: #666;
}

:root {
  --primary-color: #07c160;
  --background: #f8f8f8;
}

/* 回复输入框的按钮容器样式 */
.reply-input.buttons-container {
  display: flex;
  flex-direction: row;
  gap: 10rpx;
}

/* 回复输入框的按钮样式 */
.reply-input button {
  background-color: var(--primary-light);
  color: var(--primary-color);
  border: 1rpx solid var(--primary-color);
  border-radius: 30rpx;
  font-size: 26rpx;
  cursor: pointer;
  padding: 8rpx 20rpx;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.1);
}

/* 子评论容器样式 */
.replies-container {
  margin-left: 40rpx;
}

.sub-comment-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  padding: 6rpx 0;
}

/* 回复的字体样式 */
.reply-text {
  color: var(--primary-color);
  display: inline-block;
  margin-left: 8rpx;
  font-weight: 500;
  transition: color 0.2s ease;
}

.reply-info {
  font-size: 26rpx;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 8rpx;
  line-height: 1.4;
}

.author-name {
  font-weight: 600;
  color: var(--text-primary);
  transition: color 0.2s ease;
}

.reply-to {
  color: var(--text-tertiary);
  font-size: 24rpx;
  margin: 0 4rpx;
}

.target-name {
  font-weight: 600;
  color: var(--text-primary);
  transition: color 0.2s ease;
}

.sub-comment-text {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  word-wrap: break-word;
  padding: 4rpx 0;
  transition: color 0.2s ease;
}