const app = getApp();

// 封装 wx.request
function request(options) {
  console.log('request options',options)

  if (!/^http?:\/\//i.test(options.url)) {
    options.url = 'http://' + options.url;  // 默认添加 https://
  }
  // 合并默认配置与传入配置
  const defaultOptions = {
    url: '',
    method: 'GET',
    data: {
    },
    header: {
      'content-type': 'application/json',
    },
    success: (res) => {
      // 处理成功响应
      console.log('success to response',res)
      options.success && options.success(res.data);
    },
    fail: (err) => {
      // 处理失败情况
      console.error('请求失败:', err);
      options.fail && options.fail(err);
    }
  };

  const mergedOptions = Object.assign({}, defaultOptions, options);
  // 在请求头中添加 Token
  if (app.globalData.token) {
    mergedOptions.header['Authorization'] = `${app.globalData.token}`;
  }
  console.log('mergeOptions',mergedOptions)
  console.log('app.token in request', app.globalData.token)
  return wx.request(mergedOptions);
}

module.exports = {
  get: function(url, data, success, fail) {
    return request({ url:url,
       data, 
       success, 
       fail });
  },
  post: function(url, data, success, fail) {
    return request({
      url: url,
      method: 'POST',
      data,
      success,
      fail
    });
  }
};