// app.js
App(
  {
    towxml: require('/towxml/index'),
  onLaunch() {
    //获取本地缓存是否已经登录
    // wx.getStorage('islogin');
    // console.log('islogin_inapp',islogin)
    //获取系统信息
    try {
      const systemInfo = wx.getSystemInfoSync();
      console.log('systemInfo', systemInfo);
      this.globalData.safeArea = systemInfo.safeArea;
    } catch (error) {
      console.error('获取系统信息失败:', error);
      // 设置默认值
      this.globalData.safeArea = {
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: 375,
        height: 667
      };
    }
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)
    // 登录
    wx.login({
      success: res => {
        var code=res.code;
        if(code){
          console.log('获取用户登录凭证：'+code);
          this.globalData.code=code;
        }
        else{
          console.log('用户登录状态失败'+res.errMsg)
        }
      }
    })
  },
  globalData: {
    islogin:false,
    userInfo: {},
    code:'',
    token: '',
    safeArea:{},
    openid:''
  }
})
