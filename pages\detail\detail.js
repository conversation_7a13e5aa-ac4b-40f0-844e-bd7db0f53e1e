import article from "../../utils/post";
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    articleDetail: null,
    comments: [],
    newComment: "",
    isReplying: false,
    replyToId: null,
    replyContent: "",
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    // 并行请求文章详情和评论
    Promise.all([
      this.fetchArticleDetail(id),
      this.checkAndFetchComments(id),
    ]).catch((err) => {
      this.handleError("数据加载失败，请稍后重试", err);
    });
  },
  
  // 处理错误的通用方法
  handleError(message, error) {
    console.error(message, error);
    wx.showToast({ title: message, icon: "none" });
  },
  
  // 检查并获取评论
  checkAndFetchComments(articleId) {
    if (!this.checkMethodExists("getComments", "无法加载评论功能")) {
      return Promise.resolve([]);
    }
    return this.fetchComments(articleId);
  },

  // 检查方法存在性的公共方法
  checkMethodExists(methodName, errorMessage) {
    if (typeof article[methodName] !== "function") {
      console.error(errorMessage);
      wx.showToast({ title: errorMessage, icon: "none" });
      return false;
    }
    return true;
  },

  fetchArticleDetail(articleId) {
    if (typeof article.getArticleDetail === "function") {
      article
        .getArticleDetail(articleId)
        .then((detail) => {
          console.log("detail info", detail);
          this.setData({ articleDetail: detail });
        })
        .catch((err) => {
          console.error("加载文章详情失败:", err);
          wx.showToast({ title: "加载文章详情失败", icon: "none" });
        });
    } else {
      console.error(
        "getArticleDetail 方法不存在，请检查 ../../utils/post 文件的导出内容"
      );
      wx.showToast({ title: "无法加载文章详情", icon: "none" });
    }
  },

  fetchComments(articleId) {
    if (typeof article.getComments === "function") {
      article
        .getComments(articleId)
        .then((comments) => {
          console.log("comments info", comments);
          this.setData({ comments: comments });
        })
        .catch((err) => {
          console.error("加载评论失败:", err);
          wx.showToast({ title: "加载评论失败", icon: "none" });
        });
    } else {
      console.error(
        "getComments 方法不存在，请检查 ../../utils/post 文件的导出内容"
      );
      wx.showToast({ title: "无法加载评论", icon: "none" });
    }
  },

  handleNewCommentInput(e) {
    this.setData({ newComment: e.detail.value });
  },

  handleReplyClick(e) {
    const commentId = e.currentTarget.dataset.id;
    this.setData({ isReplying: true, replyToId: commentId, replyContent: "" });
  },

  handleReplyCancel() {
    this.setData({ isReplying: false, replyToId: null, replyContent: "" });
  },

  handleReplyContentChange(e) {
    this.setData({ replyContent: e.detail.value });
  },

  handlePublishComment() {
    if (!this.data.newComment.trim() && !this.data.replyContent.trim()) {
      return wx.showToast({ title: "内容不能为空", icon: "none" });
    }
    
    // 检查用户是否登录
    if (!app.globalData.token) {
      wx.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    
    const { id } = this.options;
    // 添加安全检查，确保userInfo存在且有id属性
    const userId = app.globalData.userInfo && app.globalData.userInfo.id ? app.globalData.userInfo.id : 0;
    
    wx.showLoading({ title: "发布中..." });
    
    if (this.data.isReplying) {
      // 发布回复
      if (!this.data.replyToId) {
        wx.hideLoading();
        return wx.showToast({ title: "回复目标不存在", icon: "none" });
      }
      
      const commentData = {
        userId: userId,
        articleId: parseInt(id),
        content: this.data.replyContent,
        parentId: this.data.replyToId
      };
      
      if (typeof article.addComment === "function") {
        article.addComment(commentData)
          .then((result) => {
            wx.hideLoading();
            wx.showToast({ title: "回复成功", icon: "success" });
            // 重新获取评论列表
            this.fetchComments(id);
            this.setData({
              isReplying: false,
              replyToId: null,
              replyContent: ""
            });
          })
          .catch((err) => {
            wx.hideLoading();
            console.error("发布回复失败:", err);
            wx.showToast({ title: typeof err === 'string' ? err : "发布回复失败", icon: "none" });
          });
      } else {
        wx.hideLoading();
        console.error("addComment 方法不存在");
        wx.showToast({ title: "评论功能暂不可用", icon: "none" });
      }
    } else {
      // 发布新评论
      const commentData = {
        userId: userId,
        articleId: parseInt(id),
        content: this.data.newComment,
        parentId: 0 // 新评论的parentId为0
      };
      
      if (typeof article.addComment === "function") {
        article.addComment(commentData)
          .then((result) => {
            wx.hideLoading();
            wx.showToast({ title: "评论成功", icon: "success" });
            // 重新获取评论列表
            this.fetchComments(id);
            this.setData({ newComment: "" });
          })
          .catch((err) => {
            wx.hideLoading();
            console.error("发布评论失败:", err);
            wx.showToast({ title: typeof err === 'string' ? err : "发布评论失败", icon: "none" });
          });
      } else {
        wx.hideLoading();
        console.error("addComment 方法不存在");
        wx.showToast({ title: "评论功能暂不可用", icon: "none" });
      }
    }
  },

  findCommentById(comments, id) {
    for (let comment of comments) {
      if (comment.id === id) return comment;
      if (comment.replies) {
        const found = this.findCommentById(comment.replies, id);
        if (found) return found;
      }
    }
    return null;
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新时重新获取文章详情和评论
    const { id } = this.options;
    this.fetchArticleDetail(id);
    if (typeof article.getComments === "function") {
      this.fetchComments(id);
    } else {
      console.error(
        "getComments 方法不存在，请检查 ../../utils/post 文件的导出内容"
      );
      wx.showToast({ title: "无法加载评论", icon: "none" });
    }
    wx.stopPullDownRefresh(); // 停止下拉刷新动画
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 可添加加载更多评论的逻辑
    console.log("页面触底，可添加加载更多评论的逻辑");
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: "分享文章",
      path: "/pages/articleDetail/articleDetail?id=" + this.options.id,
    };
  },
});