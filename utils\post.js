// 引入request模块（此处可能存在问题，见注释①）
const { post } = require('request');
// 引入自定义请求模块
const request = require('./request');
// 获取小程序应用实例
const app = getApp();

const baseURL = '222.16.24.135:54538';

// 获取文章列表函数
function list(param, categoryId, pageNum = 1, pageSize = 1000) {
  // 打印入参param
  console.log('post param', param);
  
  // 参数有效性校验
  // 校验页码：非数字或NaN时重置为1
  if (typeof pageNum !== 'number' || isNaN(pageNum)) pageNum = 1
  // 校验每页数量：非数字或NaN时重置为10
  if (typeof pageSize !== 'number' || isNaN(pageSize)) pageSize = 1000

  // 声明请求URL变量
  let ReachURL;

  // 根据参数类型构建请求路径
  if (param === 'cat_id') {
    // 基础分页参数拼接
    ReachURL = `${baseURL}/api/user/getArticlesList?pageNum=${pageNum}&pageSize=${pageSize}`;
    // 附加分类ID条件（当categoryId存在时）
    if (categoryId) ReachURL += `&categoryId=${categoryId}`;
  }
  
  // 打印最终请求地址
  console.log('request URL:', ReachURL);

  // 返回Promise对象
  return new Promise((resolve, reject) => {
    // 发起GET请求
    request.get(ReachURL, {}, 
      // 成功回调
      (res) => {
        // 安全访问嵌套数据：res.data.data.list，失败时返回空数组
        const posts = res.data?.data?.list || []; 
        console.log(' 本次获取文章数量:', posts.length);
        // 解析Promise返回文章列表
        resolve(posts);
      },
      // 失败回调
      (err) => {
        // 打印错误日志
        console.error('获取列表失败:', err);
        // 拒绝Promise并返回空数组
        reject([]); 
      }
    );
  });
}

// 获取文章详情函数
function getArticleDetail(articleId) {
  // 参数空值检查
  if (!articleId) {
    // 打印错误日志
    console.error('缺少文章ID参数');
    // 返回拒绝状态的Promise
    return Promise.reject('文章ID不能为空');
  }

  // 拼接详情请求URL
  const detailURL = `${baseURL}/api/user/details/${articleId}`;
  // 打印详情请求地址
  console.log('文章详情请求URL:', detailURL);

  // 返回Promise对象
  return new Promise((resolve, reject) => {
    // 发起GET请求
    request.get(detailURL, {},
      // 成功回调
      (res) => {
        // 安全访问响应数据
        const detailData = res.data?.data; 
        
        // 数据不存在时的处理
        if (!detailData) {
          // 打印警告日志
          console.warn('文章不存在或数据格式错误');
          // 拒绝Promise
          return reject('文章不存在');
        }

        // 打印详情数据
        console.log('文章详情数据:', detailData);
        // 解析Promise返回详情数据
        resolve(detailData);
      },
      // 失败回调
      (err) => {
        // 打印错误日志
        console.error('获取详情失败:', err);
        // 根据状态码返回不同错误信息
        reject(err.statusCode === 404 ? '文章不存在' : '获取详情失败');
      }
    );
  });
}

// 导出模块方法
module.exports = { list, getArticleDetail };