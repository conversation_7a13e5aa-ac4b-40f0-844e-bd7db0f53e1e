/* 分类导航栏样式 */
.category-nav {
  display: flex;
  padding: 10px;
  gap: 10px;
  background-color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  width: 100%; /* 确保导航栏占满宽度 */
}

/* 分类按钮样式 */
.category-item {
  flex: none;
  text-align: center;
  padding: 5px 10px;
  background-color: #fff;
  margin: 0 5px;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 100;
  cursor: pointer;
}

/* 激活状态的分类按钮样式 */
.category-item.active {
  background-color: #007aff;
  color: white;
  font-weight: bold;
}

/* 文章列表容器样式 */
.article-container {
  padding: 8px;
  margin-top: 80px; /* 为了避开固定的导航栏，给文章列表留出足够的空间 */
}

/* 文章项样式 */
.article-item {
  margin-bottom: 0px;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  border-bottom: 1px solid #ccc;
}

/* 文章内容区域 */
.article-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 文章头部样式 */
.article-header {
  margin-bottom: 10px;
}

/* 文章标题样式 */
.article-title {
  font-size: 16px;
  font-weight: bold;
}

/* 文章元数据（作者、时间）样式 */
.article-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #888;
}

/* 文章作者样式 */
.article-author {
  margin-right: 10px;
  white-space: nowrap;
}

/* 文章时间样式 */
.article-time {
  font-style: italic;
  white-space: nowrap;
}

/* 文章图片样式 */
.article-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 5px;
  margin-left: 20px;
}

/* 搜索框样式 */
.search-box {
  padding: 8px 10px; /* 减少上下的内边距 */
  background-color: #ffffff; /* 改为白色背景 */
  margin-top: 60px; /* 增加顶部间距，使搜索框下移 */
  margin-bottom: 10px; /* 增加与文章列表之间的间距 */
  width: 90%; /* 设置搜索框的宽度为 90%，这样会更窄 */
  max-width: 350px; /* 设置最大宽度，防止过宽 */
  margin-left: auto; /* 水平居中 */
  margin-right: auto; /* 水平居中 */
}

/* 搜索框输入框样式 */
.search-input {
  width: 100%;
  padding: 8px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-top: 0; /* 取消搜索框内部的间距 */
}



/* 现代化分页组件样式 */
.pagination-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  padding: 16rpx 0;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pagination-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
}

/* 页码数字区域 */
.pagination-numbers {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20rpx;
}

/* 单个页码按钮 */
.pagination-number {
  min-width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  transition: all 0.2s ease;
}

/* 当前页码样式 */
.pagination-number.current {
  background-color: #4e89fd;
  color: white;
  font-weight: 500;
  box-shadow: 0 4rpx 8rpx rgba(78, 137, 253, 0.2);
}

/* 页码按钮点击效果 */
.pagination-number:not(.current):active {
  background-color: #e8f0ff;
  transform: scale(0.95);
}

/* 省略号样式 */
.pagination-ellipsis {
  min-width: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
  margin: 0 4rpx;
}

/* 箭头按钮样式 */
.pagination-arrow {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  transition: all 0.2s ease;
}

/* 箭头按钮激活状态 */
.pagination-arrow:not(.disabled):active {
  background-color: #e8f0ff;
  transform: scale(0.95);
}

/* 禁用状态的箭头按钮 */
.pagination-arrow.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 箭头图标基础样式 */
.pagination-icon {
  width: 16rpx;
  height: 16rpx;
  border-top: 3rpx solid #666;
  border-right: 3rpx solid #666;
  transition: all 0.2s ease;
}

/* 上一页箭头 */
.pagination-prev {
  transform: rotate(-135deg);
  margin-right: -6rpx;
}

/* 下一页箭头 */
.pagination-next {
  transform: rotate(45deg);
  margin-left: -6rpx;
}

/* 页码信息样式 */
.pagination-info {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  text-align: center;
}


/* 搜索部分*/
.searchbox {
  margin: auto;
  margin-top: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10rpx 20rpx;
  box-sizing: border-box;
  width: 90%;
  background-color: white;
  border-radius: 40rpx;
  margin-top: 100rpx;  /* 增大顶部外边距 */
}
.searchimage {
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx 20rpx 10rpx 10rpx;
}
.searchinput {
  display: inline-block;
  width: 90%;
}

.logo {
  font-size: 30rpx;
  color: #666;
}

.no-articles {
  text-align: center;
  font-size: 16px;
  color: #999;
  margin: 20px 0;
}
