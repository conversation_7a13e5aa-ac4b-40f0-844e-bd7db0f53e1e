<!--pages/gpt/gpt.wxml-->
<!-- 顶部标题栏 -->
<view class="header">
  <view class="header-title">安全智解</view>
</view>

<view class="chat-container">
  <scroll-view scroll-y="{{true}}" class="message-scroll-view" scroll-into-view="{{scrollToView}}">
    <view class="message {{item.type === 'user'? 'user-message' : 'ai-message'}}"
          wx:for="{{messages}}"
          wx:key="index"
          id="msg-{{index}}">
      <view wx:if="{{item.type === 'ai'}}" class="message-header">
        <image class="ai-avatar" src="/img/tabar_icon/彩<EMAIL>"></image>
      </view>
      <view class="message-content">
        <block wx:if="{{item.type === 'ai'}}">
          <towxml nodes="{{result}}" />
        </block>
        <block wx:else>
          <text>{{item.content}}</text>
        </block>
      </view>
    </view>
  </scroll-view>

  <!-- 底部输入区域 -->
  <view class="input-container">
    <view class="input-wrapper {{isFocused ? 'focused' : ''}}">
      <input type="text"
             placeholder="输入您的问题..."
             placeholder-style="color: #999;"
             class="input-field"
             value="{{inputValue}}"
             bindinput="handleInput"
             bindconfirm="sendMessage"
             bindfocus="handleFocus"
             bindblur="handleBlur"/>
    </view>
    <view class="send-button" bindtap="sendMessage">
      <image class="send-icon" src="/img/tabar_icon/发送.png" mode="aspectFit"></image>
    </view>
  </view>
</view>