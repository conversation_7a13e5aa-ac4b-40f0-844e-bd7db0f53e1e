<!--pages/gpt/gpt.wxml-->
<!-- 页面头部 -->
<view class="header">
  <view class="header-content">
    <view class="header-title">AI 智能助手</view>
    <view class="header-actions">
      <view class="header-action-btn">
        <image class="header-action-icon" src="/img/tabar_icon/more.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>
</view>

<!-- 聊天主容器 -->
<view class="chat-container">
  <scroll-view
    scroll-y="{{true}}"
    class="message-scroll-view"
    scroll-into-view="{{scrollToView}}"
    enhanced="{{true}}"
    show-scrollbar="{{false}}"
    bounces="{{true}}">

    <!-- 欢迎消息 -->
    <view class="welcome-section" wx:if="{{messages.length === 0}}">
      <image class="welcome-logo" src="/img/tabar_icon/彩<EMAIL>" mode="aspectFit"></image>
      <view class="welcome-title">欢迎使用AI助手</view>
      <view class="welcome-subtitle">有什么我可以帮您的吗？</view>
    </view>

    <!-- 消息列表 -->
    <view
      class="message {{item.type === 'user'? 'user-message' : 'ai-message'}}"
      wx:for="{{messages}}"
      wx:key="index"
      id="msg-{{index}}">

      <!-- AI消息 -->
      <block wx:if="{{item.type === 'ai'}}">
        <view class="message-header">
          <image class="ai-avatar" src="/img/tabar_icon/彩<EMAIL>" mode="aspectFill"></image>
          <view class="ai-info">
            <text class="ai-name">AI助手</text>
          </view>
        </view>
        <view class="message-content ai-content">
          <towxml nodes="{{result}}" />
        </view>
      </block>

      <!-- 用户消息 -->
      <block wx:else>
        <view class="message-content user-content">
          <text>{{item.content}}</text>
        </view>
      </block>
    </view>

    <!-- 加载指示器 -->
    <view class="loading-indicator" wx:if="{{isLoading}}">
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
    </view>
  </scroll-view>

  <!-- 底部输入区域 -->
  <view class="input-area">
    <view class="input-container {{isFocused ? 'input-focused' : ''}}">
      <input
        type="text"
        placeholder="输入您的问题..."
        placeholder-style="color: #999;"
        class="input-field"
        value="{{inputValue}}"
        bindinput="handleInput"
        bindconfirm="sendMessage"
        bindfocus="handleFocus"
        bindblur="handleBlur"
        adjust-position="{{true}}"
        cursor-spacing="20"/>
      <view class="send-button {{inputValue ? 'active' : ''}}" bindtap="sendMessage">
        <image class="send-icon" src="/img/tabar_icon/send.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>
</view>